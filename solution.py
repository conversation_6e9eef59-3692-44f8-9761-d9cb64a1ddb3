import os
import torch
import torch.nn as nn
import torch.nn.functional as F

# -----------------------------------------------------------------------------
# Complex-Valued Neural Network Components (CV-CNN Architecture)
# -----------------------------------------------------------------------------

class ComplexConv2d(nn.Module):
    """Complex-valued 2D convolution using efficient Gaussian multiplication trick.

    Implements complex convolution: Conv(X_R + iX_I, W_R + iW_I)
    = (Conv(X_R, W_R) - Conv(X_I, W_I)) + i(Conv(X_R, W_I) + Conv(X_I, W_R))

    Uses 3 real convolutions instead of 4 for 25% efficiency improvement.
    """

    def __init__(self, in_channels, out_channels, kernel_size, stride=1,
                 padding=0, dilation=1, groups=1, bias=True):
        super().__init__()

        # Three real convolutions for Gaussian multiplication trick
        self.conv_r = nn.Conv2d(in_channels, out_channels, kernel_size, stride,
                               padding, dilation, groups, bias)
        self.conv_i = nn.Conv2d(in_channels, out_channels, kernel_size, stride,
                               padding, dilation, groups, bias)
        self.conv_h = nn.Conv2d(in_channels, out_channels, kernel_size, stride,
                               padding, dilation, groups, bias)

    def forward(self, x):
        """Forward pass with complex input tensor.

        Args:
            x: Complex tensor [B, C, H, W] (torch.cfloat)
        Returns:
            Complex tensor [B, C_out, H_out, W_out] (torch.cfloat)
        """
        if not torch.is_complex(x):
            raise ValueError("Input must be complex tensor (torch.cfloat)")

        x_r = x.real
        x_i = x.imag

        # Gaussian multiplication trick: 3 convolutions instead of 4
        # Let A = Conv(x_r, w_r), B = Conv(x_i, w_i), C = Conv(x_r + x_i, w_r + w_i)
        # Then: real_part = A - B, imag_part = C - A - B
        A = self.conv_r(x_r)
        B = self.conv_i(x_i)
        C = self.conv_h(x_r + x_i)

        real_part = A - B
        imag_part = C - A - B

        return torch.complex(real_part, imag_part)


class ComplexConvTranspose2d(nn.Module):
    """Complex-valued 2D transposed convolution (deconvolution)."""

    def __init__(self, in_channels, out_channels, kernel_size, stride=1,
                 padding=0, output_padding=0, groups=1, bias=True, dilation=1):
        super().__init__()

        # Three real transposed convolutions for Gaussian multiplication trick
        self.conv_r = nn.ConvTranspose2d(in_channels, out_channels, kernel_size,
                                        stride, padding, output_padding, groups, bias, dilation)
        self.conv_i = nn.ConvTranspose2d(in_channels, out_channels, kernel_size,
                                        stride, padding, output_padding, groups, bias, dilation)
        self.conv_h = nn.ConvTranspose2d(in_channels, out_channels, kernel_size,
                                        stride, padding, output_padding, groups, bias, dilation)

    def forward(self, x):
        """Forward pass with complex input tensor."""
        if not torch.is_complex(x):
            raise ValueError("Input must be complex tensor (torch.cfloat)")

        x_r = x.real
        x_i = x.imag

        # Gaussian multiplication trick
        A = self.conv_r(x_r)
        B = self.conv_i(x_i)
        C = self.conv_h(x_r + x_i)

        real_part = A - B
        imag_part = C - A - B

        return torch.complex(real_part, imag_part)


class ModReLU(nn.Module):
    """Magnitude-Phase activation function for complex tensors.

    modReLU(r*e^(iθ)) = ReLU(r + b) * e^(iθ)

    Preserves phase information while applying nonlinearity to magnitude.
    """

    def __init__(self, bias_init=0.0):
        super().__init__()
        self.bias = nn.Parameter(torch.tensor(bias_init, dtype=torch.float32))

    def forward(self, x):
        """Apply modReLU activation to complex tensor.

        Args:
            x: Complex tensor (torch.cfloat)
        Returns:
            Complex tensor with modReLU applied
        """
        if not torch.is_complex(x):
            raise ValueError("Input must be complex tensor (torch.cfloat)")

        # Compute magnitude and phase
        magnitude = torch.abs(x)
        phase = torch.angle(x)

        # Apply ReLU to magnitude with learnable bias
        activated_magnitude = F.relu(magnitude + self.bias)

        # Reconstruct complex number with activated magnitude and preserved phase
        return activated_magnitude * torch.exp(1j * phase)


class ComplexLayerNorm(nn.Module):
    """Complex-valued Layer Normalization.

    Normalizes complex tensors while preserving complex structure.
    """

    def __init__(self, normalized_shape, eps=1e-6):
        super().__init__()
        self.normalized_shape = normalized_shape
        self.eps = eps

        # Learnable parameters for real and imaginary parts
        self.weight_r = nn.Parameter(torch.ones(normalized_shape))
        self.weight_i = nn.Parameter(torch.zeros(normalized_shape))
        self.bias_r = nn.Parameter(torch.zeros(normalized_shape))
        self.bias_i = nn.Parameter(torch.zeros(normalized_shape))

    def forward(self, x):
        """Apply complex layer normalization.

        Args:
            x: Complex tensor (torch.cfloat)
        Returns:
            Normalized complex tensor
        """
        if not torch.is_complex(x):
            raise ValueError("Input must be complex tensor (torch.cfloat)")

        # Compute mean and variance for complex tensor
        mean = torch.mean(x, dim=-1, keepdim=True)
        var = torch.mean(torch.abs(x - mean) ** 2, dim=-1, keepdim=True)

        # Normalize
        x_normalized = (x - mean) / torch.sqrt(var + self.eps)

        # Apply learnable parameters
        weight = torch.complex(self.weight_r, self.weight_i)
        bias = torch.complex(self.bias_r, self.bias_i)

        return x_normalized * weight + bias


# -----------------------------------------------------------------------------
# Legacy ConvNeXt components removed - replaced with Complex-Valued U-Net
# -----------------------------------------------------------------------------


# -----------------------------------------------------------------------------
# Exponential Map Unitary Layer (CV-CNN Architecture)
# -----------------------------------------------------------------------------
class ExponentialMapUnitaryLayer(nn.Module):
    """Exponential Map for generating unitary matrices: U = exp(A) where A† = -A.

    This is the recommended approach from the document for explicit unitary parameterization.
    Any unitary matrix U can be expressed as U = exp(A) where A is skew-Hermitian.
    """

    def __init__(self, matrix_dim, eps=1e-8):
        super().__init__()
        self.matrix_dim = matrix_dim
        self.eps = eps
        print(f"[ExponentialMapUnitaryLayer] Initialized for {matrix_dim}x{matrix_dim} unitary matrices")

    def forward(self, A):
        """Apply Exponential Map: U = exp(A) where A is skew-Hermitian.

        Args:
            A: skew-Hermitian matrix (batch_size, M, M) - complex tensor
        Returns:
            U: unitary matrix (batch_size, M, M) - complex tensor
        """
        if not torch.is_complex(A):
            raise ValueError("Input matrix A must be complex (skew-Hermitian)")

        # Verify skew-Hermitian property (A† = -A) for debugging
        if torch.is_grad_enabled():
            A_hermitian = torch.conj(A).transpose(-2, -1)
            skew_error = torch.mean(torch.abs(A + A_hermitian))
            if skew_error > 1e-3:  # Tolerance for numerical errors
                print(f"Warning: Input matrix not properly skew-Hermitian, error: {skew_error:.6f}")

        # Apply matrix exponential to get unitary matrix
        U = torch.matrix_exp(A)

        return U

    def check_unitarity(self, U):
        """Check unitarity of the output matrix: U†U ≈ I."""
        U_hermitian = torch.conj(U).transpose(-2, -1)
        UHU = torch.matmul(U_hermitian, U)
        I = torch.eye(U.size(-1), device=U.device, dtype=U.dtype)
        error = torch.mean(torch.abs(UHU - I) ** 2)
        return error.item()


def vector_to_skew_hermitian(vec, matrix_dim):
    """Convert parameter vector to skew-Hermitian matrix A where A† = -A.

    Args:
        vec: (batch_size, matrix_dim^2) parameter vector
        matrix_dim: dimension of the output matrix
    Returns:
        A: (batch_size, matrix_dim, matrix_dim) skew-Hermitian matrix (complex)
    """
    batch_size = vec.shape[0]
    device = vec.device
    dtype = torch.complex64 if vec.dtype == torch.float32 else torch.complex128

    # Reshape vector to matrix form
    vec_reshaped = vec.view(batch_size, matrix_dim, matrix_dim)

    # Create skew-Hermitian matrix
    # Diagonal elements are purely imaginary
    diag_indices = torch.arange(matrix_dim, device=device)
    diag_imag = vec_reshaped[:, diag_indices, diag_indices]

    # Off-diagonal elements: upper triangle defines the matrix
    triu_indices = torch.triu_indices(matrix_dim, matrix_dim, offset=1, device=device)

    # Create complex matrix
    A = torch.zeros(batch_size, matrix_dim, matrix_dim, dtype=dtype, device=device)

    # Set diagonal to purely imaginary
    A[:, diag_indices, diag_indices] = 1j * diag_imag

    # Set upper triangle (can be complex in general case)
    if triu_indices[0].numel() > 0:  # Check if there are off-diagonal elements
        upper_vals = vec_reshaped[:, triu_indices[0], triu_indices[1]]
        A[:, triu_indices[0], triu_indices[1]] = upper_vals

        # Set lower triangle as negative conjugate transpose of upper triangle
        A[:, triu_indices[1], triu_indices[0]] = -torch.conj(upper_vals)

    return A


# -----------------------------------------------------------------------------
# Unitary Matrix Generator with Exponential Map (CV-CNN Architecture)
# -----------------------------------------------------------------------------
class UnitaryGenerator(nn.Module):
    """Unitary matrix generator using Exponential Map for strict unitary constraints.

    Converts parameter vectors to unitary matrices via U = exp(A) where A is skew-Hermitian.
    This ensures mathematically exact unitary matrices as required by CV-CNN architecture.
    """

    def __init__(self, matrix_dim, rank):
        super().__init__()
        self.matrix_dim = matrix_dim
        self.rank = rank

        # For complex skew-Hermitian matrices: A† = -A
        # We need matrix_dim^2 real parameters for a full skew-Hermitian matrix
        self.param_dim = matrix_dim * matrix_dim

        # Exponential map unitary layer
        self.exp_map = ExponentialMapUnitaryLayer(matrix_dim)

        print(f"[UnitaryGenerator] Initialized with Exponential Map for {matrix_dim}x{matrix_dim} -> {matrix_dim}x{rank} unitary matrices")

    def forward(self, param_vec):
        """Convert parameter vector to unitary matrix using Exponential Map.

        Args:
            param_vec: (batch_size, param_dim) parameter vector
        Returns:
            unitary_mat: (batch_size, matrix_dim, rank, 2) truncated unitary matrix in real-imag format
        """
        batch_size = param_vec.shape[0]

        # Convert vector to skew-Hermitian matrix
        A = vector_to_skew_hermitian(param_vec, self.matrix_dim)

        # Apply Exponential Map to get unitary matrix: U = exp(A)
        U = self.exp_map(A)  # (batch_size, matrix_dim, matrix_dim) - complex

        # Truncate to get the first R columns
        U_truncated = U[:, :, :self.rank]  # (batch_size, matrix_dim, rank) - complex

        # Convert complex tensor to real-imaginary format for output
        U_ri = torch.stack([U_truncated.real, U_truncated.imag], dim=-1)

        return U_ri  # (batch_size, matrix_dim, rank, 2)

    def check_unitarity(self, param_vec):
        """Check unitarity of generated matrix for debugging."""
        with torch.no_grad():
            A = vector_to_skew_hermitian(param_vec, self.matrix_dim)
            U = self.exp_map(A)
            return self.exp_map.check_unitarity(U)


# -----------------------------------------------------------------------------
# Complex-Valued U-Net Encoder (CV-CNN Architecture)
# -----------------------------------------------------------------------------
class ComplexUNetEncoder(nn.Module):
    """Complex-valued U-Net encoder with multi-scale feature extraction."""

    def __init__(self, in_channels=1, base_channels=64):
        super().__init__()

        # Encoder path with complex convolutions
        self.enc1 = nn.Sequential(
            ComplexConv2d(in_channels, base_channels, 3, padding=1),
            ComplexLayerNorm(base_channels),
            ModReLU(),
            ComplexConv2d(base_channels, base_channels, 3, padding=1),
            ComplexLayerNorm(base_channels),
            ModReLU()
        )

        self.enc2 = nn.Sequential(
            ComplexConv2d(base_channels, base_channels*2, 3, stride=2, padding=1),  # Downsample
            ComplexLayerNorm(base_channels*2),
            ModReLU(),
            ComplexConv2d(base_channels*2, base_channels*2, 3, padding=1),
            ComplexLayerNorm(base_channels*2),
            ModReLU()
        )

        self.enc3 = nn.Sequential(
            ComplexConv2d(base_channels*2, base_channels*4, 3, stride=2, padding=1),  # Downsample
            ComplexLayerNorm(base_channels*4),
            ModReLU(),
            ComplexConv2d(base_channels*4, base_channels*4, 3, padding=1),
            ComplexLayerNorm(base_channels*4),
            ModReLU()
        )

        self.enc4 = nn.Sequential(
            ComplexConv2d(base_channels*4, base_channels*8, 3, stride=2, padding=1),  # Downsample
            ComplexLayerNorm(base_channels*8),
            ModReLU(),
            ComplexConv2d(base_channels*8, base_channels*8, 3, padding=1),
            ComplexLayerNorm(base_channels*8),
            ModReLU()
        )

        # Bottleneck
        self.bottleneck = nn.Sequential(
            ComplexConv2d(base_channels*8, base_channels*16, 3, stride=2, padding=1),  # Downsample
            ComplexLayerNorm(base_channels*16),
            ModReLU(),
            ComplexConv2d(base_channels*16, base_channels*16, 3, padding=1),
            ComplexLayerNorm(base_channels*16),
            ModReLU()
        )

    def forward(self, x):
        """Forward pass returning features at different scales for skip connections."""
        # Encoder path
        e1 = self.enc1(x)      # [B, 64, H, W]
        e2 = self.enc2(e1)     # [B, 128, H/2, W/2]
        e3 = self.enc3(e2)     # [B, 256, H/4, W/4]
        e4 = self.enc4(e3)     # [B, 512, H/8, W/8]

        # Bottleneck
        bottleneck = self.bottleneck(e4)  # [B, 1024, H/16, W/16]

        return [e1, e2, e3, e4, bottleneck]


class ComplexUNetDecoder(nn.Module):
    """Complex-valued U-Net decoder with skip connections."""

    def __init__(self, base_channels=64):
        super().__init__()

        # Decoder path with complex transposed convolutions
        self.dec4 = nn.Sequential(
            ComplexConvTranspose2d(base_channels*16, base_channels*8, 2, stride=2),  # Upsample
            ComplexLayerNorm(base_channels*8),
            ModReLU()
        )
        self.dec4_conv = nn.Sequential(
            ComplexConv2d(base_channels*16, base_channels*8, 3, padding=1),  # After concat
            ComplexLayerNorm(base_channels*8),
            ModReLU(),
            ComplexConv2d(base_channels*8, base_channels*8, 3, padding=1),
            ComplexLayerNorm(base_channels*8),
            ModReLU()
        )

        self.dec3 = nn.Sequential(
            ComplexConvTranspose2d(base_channels*8, base_channels*4, 2, stride=2),  # Upsample
            ComplexLayerNorm(base_channels*4),
            ModReLU()
        )
        self.dec3_conv = nn.Sequential(
            ComplexConv2d(base_channels*8, base_channels*4, 3, padding=1),  # After concat
            ComplexLayerNorm(base_channels*4),
            ModReLU(),
            ComplexConv2d(base_channels*4, base_channels*4, 3, padding=1),
            ComplexLayerNorm(base_channels*4),
            ModReLU()
        )

        self.dec2 = nn.Sequential(
            ComplexConvTranspose2d(base_channels*4, base_channels*2, 2, stride=2),  # Upsample
            ComplexLayerNorm(base_channels*2),
            ModReLU()
        )
        self.dec2_conv = nn.Sequential(
            ComplexConv2d(base_channels*4, base_channels*2, 3, padding=1),  # After concat
            ComplexLayerNorm(base_channels*2),
            ModReLU(),
            ComplexConv2d(base_channels*2, base_channels*2, 3, padding=1),
            ComplexLayerNorm(base_channels*2),
            ModReLU()
        )

        self.dec1 = nn.Sequential(
            ComplexConvTranspose2d(base_channels*2, base_channels, 2, stride=2),  # Upsample
            ComplexLayerNorm(base_channels),
            ModReLU()
        )
        self.dec1_conv = nn.Sequential(
            ComplexConv2d(base_channels*2, base_channels, 3, padding=1),  # After concat
            ComplexLayerNorm(base_channels),
            ModReLU(),
            ComplexConv2d(base_channels, base_channels, 3, padding=1),
            ComplexLayerNorm(base_channels),
            ModReLU()
        )

    def forward(self, encoder_features):
        """Forward pass with skip connections from encoder."""
        e1, e2, e3, e4, bottleneck = encoder_features

        # Decoder path with skip connections
        d4 = self.dec4(bottleneck)
        d4 = torch.cat([d4, e4], dim=1)  # Skip connection
        d4 = self.dec4_conv(d4)

        d3 = self.dec3(d4)
        d3 = torch.cat([d3, e3], dim=1)  # Skip connection
        d3 = self.dec3_conv(d3)

        d2 = self.dec2(d3)
        d2 = torch.cat([d2, e2], dim=1)  # Skip connection
        d2 = self.dec2_conv(d2)

        d1 = self.dec1(d2)
        d1 = torch.cat([d1, e1], dim=1)  # Skip connection
        d1 = self.dec1_conv(d1)

        return d1  # [B, base_channels, H, W]


# -----------------------------------------------------------------------------
# CV-CNN SVDNet with Complex-Valued U-Net + Exponential Map Unitary Heads
# -----------------------------------------------------------------------------
class SVDNet(nn.Module):
    """CV-CNN architecture: Complex-Valued U-Net + Exponential Map Unitary Heads.

    Implements the recommended CV-CNN architecture from the document:
    - Complex-valued U-Net backbone for channel denoising and feature extraction
    - Exponential Map unitary heads for strict orthogonality constraints
    - Native complex number processing throughout the network
    """

    def __init__(self, dim: int = 64, rank: int = 32, weight_path: str = "svdnet.pth"):
        super().__init__()
        self.dim = dim  # Antenna dimension (M == N)
        self.rank = rank

        print(f"[CV-CNN SVDNet] Initializing with Complex-Valued U-Net + Exponential Map Unitary Heads")
        print(f"[CV-CNN SVDNet] Matrix dimensions: {dim}x{dim}, Rank: {rank}")

        # --------------------------- Complex-Valued U-Net Backbone ----------------------------
        base_channels = 64
        self.encoder = ComplexUNetEncoder(in_channels=1, base_channels=base_channels)
        self.decoder = ComplexUNetDecoder(base_channels=base_channels)

        # ------------------------ Multi-task Prediction Heads ----------------------------
        # Global average pooling to get feature vector from spatial features
        self.global_pool = nn.AdaptiveAvgPool2d(1)

        # Input dimension for heads: 2*base_channels (real + imaginary parts)
        head_input_dim = 2 * base_channels

        # U prediction head - generates parameters for unitary matrix U
        self.head_U = nn.Sequential(
            nn.Linear(head_input_dim, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, dim * dim),  # Parameters for skew-Hermitian matrix
        )

        # V prediction head - generates parameters for unitary matrix V
        self.head_V = nn.Sequential(
            nn.Linear(head_input_dim, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, dim * dim),  # Parameters for skew-Hermitian matrix
        )

        # S prediction head - generates singular values (real-valued)
        self.head_S = nn.Sequential(
            nn.Linear(head_input_dim, 128),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(128, rank),
            nn.Softplus()  # Ensure non-negative singular values
        )

        # ----------------------- Exponential Map Unitary Generators ------------------------
        self.generator_U = UnitaryGenerator(dim, rank)
        self.generator_V = UnitaryGenerator(dim, rank)

        # ------------------------ Weight loading ----------------------------
        if os.path.isfile(weight_path):
            state = torch.load(weight_path, map_location="cpu")
            try:
                self.load_state_dict(state, strict=False)
                print(f"[CV-CNN SVDNet] Loaded weights from {weight_path} (strict=False)")
            except RuntimeError as e:
                print(f"[CV-CNN SVDNet] Weight loading failed: {e}. Proceeding with random init.")

    # ---------------------------------------------------------------------
    def forward(self, x: torch.Tensor):
        """CV-CNN forward pass with native complex processing.

        Args:
            x: Input channel matrix, shape [M, N, 2] or [B, M, N, 2] (real-imag format)
        Returns:
            U: Left unitary matrix [B, M, R, 2] or [M, R, 2] (real-imag format)
            S: Singular values [B, R] or [R] (real-valued)
            V: Right unitary matrix [B, N, R, 2] or [N, R, 2] (real-imag format)
        """
        if x.ndim == 3:
            x = x.unsqueeze(0)
        if x.shape[-1] != 2:
            raise ValueError("Input last dim must be 2 (real/imag)")
        B = x.size(0)

        # Convert to native complex tensor for processing
        x_complex = torch.view_as_complex(x)  # [B, M, N] complex

        # Add channel dimension for U-Net processing
        x_complex = x_complex.unsqueeze(1)  # [B, 1, M, N] complex

        # Complex-valued U-Net encoder: extract hierarchical features
        encoder_features = self.encoder(x_complex)  # List of complex features at different scales

        # Complex-valued U-Net decoder: reconstruct features with skip connections
        decoded_feat = self.decoder(encoder_features)  # [B, base_channels, M, N] complex

        # Global average pooling to get feature vector
        pooled_feat = self.global_pool(decoded_feat)  # [B, base_channels, 1, 1] complex
        pooled_feat = pooled_feat.flatten(1)  # [B, base_channels] complex

        # Convert to real tensor for prediction heads (since nn.Linear doesn't support complex)
        # We'll process real and imaginary parts separately
        feat_real = pooled_feat.real  # [B, base_channels]
        feat_imag = pooled_feat.imag  # [B, base_channels]
        feat_combined = torch.cat([feat_real, feat_imag], dim=1)  # [B, 2*base_channels]

        # Update head input dimensions to account for real+imag
        # Multi-task prediction heads
        # U head: predict parameters for unitary matrix U
        U_params = self.head_U(feat_combined)  # [B, dim*dim]

        # V head: predict parameters for unitary matrix V
        V_params = self.head_V(feat_combined)  # [B, dim*dim]

        # S head: predict singular values (real-valued)
        S = self.head_S(feat_combined)  # [B, rank]

        # Generate unitary matrices using Exponential Map
        U = self.generator_U(U_params)  # [B, dim, rank, 2]
        V = self.generator_V(V_params)  # [B, dim, rank, 2]

        # Remove batch dim if B==1 (to match demo expectations)
        if B == 1:
            U = U.squeeze(0)
            V = V.squeeze(0)
            S = S.squeeze(0)

        return U, S, V

    # ------------------------------------------------------------------
    @staticmethod
    def _to_complex(mat: torch.Tensor) -> torch.Tensor:
        """Convert real-imag stacked tensor [..., 2] → complex."""
        return torch.complex(mat[..., 0], mat[..., 1])

    @staticmethod
    def _to_ri(mat: torch.Tensor) -> torch.Tensor:
        """Convert complex tensor → real-imag stacked."""
        return torch.stack((mat.real, mat.imag), dim=-1)

    def get_model_complexity(self):
        """Calculate model complexity in terms of parameters and FLOPs."""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)

        return {
            'total_params': total_params,
            'trainable_params': trainable_params,
            'model_size_mb': total_params * 4 / (1024 * 1024)  # Assuming float32
        }