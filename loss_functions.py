#!/usr/bin/env python3
"""
CV-CNN Loss Functions Aligned with AE Metric

This module implements loss functions precisely aligned with the AE evaluation metric
for the CV-CNN architecture with complex-valued processing.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F


def to_complex(t):
    """Convert real-imaginary tensor to complex tensor.

    Args:
        t: Tensor with shape [..., 2] where last dim is [real, imag]
    Returns:
        Complex tensor with shape [...]
    """
    return torch.view_as_complex(t)


def frobenius_norm_complex(t):
    """Compute Frobenius norm for complex tensor.

    Args:
        t: Complex tensor
    Returns:
        Real tensor with Frobenius norm
    """
    return torch.norm(t, p='fro', dim=(-2, -1))


class CVCNNAEAlignedLoss(nn.Module):
    """
    CV-CNN Loss Function Precisely Aligned with AE Metric.

    Implements the exact AE evaluation formula:
    AE = ||H_label - U*S*V^H||_F / ||H_label||_F + ||U^H*U - I||_F + ||V^H*V - I||_F

    This ensures training optimization directly minimizes the competition evaluation metric.
    """

    def __init__(self, lambda_rec=1.0, lambda_orth_u=1.0, lambda_orth_v=1.0, eps=1e-8):
        super().__init__()
        # Use equal weights as in AE formula (can be adjusted for training stability)
        self.lambda_rec = lambda_rec
        self.lambda_orth_u = lambda_orth_u
        self.lambda_orth_v = lambda_orth_v
        self.eps = eps

        print(f"[CVCNNAEAlignedLoss] Initialized with weights: rec={lambda_rec}, orth_u={lambda_orth_u}, orth_v={lambda_orth_v}")

    def forward(self, U, S, V, H_gt):
        """
        Compute loss exactly matching AE metric computation.

        Args:
            U: Left unitary matrix [B, M, R, 2] (real-imag format)
            S: Singular values [B, R] (real-valued)
            V: Right unitary matrix [B, N, R, 2] (real-imag format)
            H_gt: Ground truth channel [B, M, N, 2] (real-imag format)

        Returns:
            Dictionary containing individual loss components and total loss
        """
        # Convert to complex tensors for computation
        U_c = to_complex(U)  # [B, M, R]
        V_c = to_complex(V)  # [B, N, R]
        H_gt_c = to_complex(H_gt)  # [B, M, N]

        # 1. Normalized reconstruction error: ||H_label - U*S*V^H||_F / ||H_label||_F
        recon_loss = self._compute_reconstruction_error(U_c, S, V_c, H_gt_c)

        # 2. Left orthogonality error: ||U^H*U - I||_F
        orth_loss_u = self._compute_orthogonality_error(U_c)

        # 3. Right orthogonality error: ||V^H*V - I||_F
        orth_loss_v = self._compute_orthogonality_error(V_c)

        # 4. Total weighted loss
        total_loss = (self.lambda_rec * recon_loss +
                     self.lambda_orth_u * orth_loss_u +
                     self.lambda_orth_v * orth_loss_v)

        return {
            'total_loss': total_loss,
            'reconstruction_loss': recon_loss,
            'orthogonality_loss_u': orth_loss_u,
            'orthogonality_loss_v': orth_loss_v,
            'ae_metric_approximation': recon_loss + orth_loss_u + orth_loss_v
        }

    def _compute_reconstruction_error(self, U_c, S, V_c, H_gt_c):
        """
        Compute normalized reconstruction error exactly as in AE metric.

        Formula: ||H_label - U*S*V^H||_F / ||H_label||_F
        """
        # Reconstruct channel: H_pred = U @ diag(S) @ V^H
        # Broadcasting singular values: U * S -> [B, M, R] * [B, 1, R] = [B, M, R]
        U_S = U_c * S.unsqueeze(1)
        H_pred = torch.matmul(U_S, torch.conj(V_c).transpose(-2, -1))  # [B, M, N]

        # Compute Frobenius norms
        reconstruction_error = frobenius_norm_complex(H_pred - H_gt_c)  # [B]
        h_norm = frobenius_norm_complex(H_gt_c)  # [B]

        # Normalized error
        normalized_error = reconstruction_error / (h_norm + self.eps)

        return torch.mean(normalized_error)

    def _compute_orthogonality_error(self, matrix):
        """
        Compute orthogonality error: ||U^H @ U - I||_F

        Args:
            matrix: Complex unitary matrix [B, M, R]
        Returns:
            Orthogonality error (scalar)
        """
        # Compute U^H @ U
        matrix_H = torch.conj(matrix).transpose(-2, -1)  # [B, R, M]
        product = torch.matmul(matrix_H, matrix)  # [B, R, R]

        # Create identity matrix
        R = matrix.size(-1)
        I = torch.eye(R, device=matrix.device, dtype=matrix.dtype)
        I = I.expand_as(product)  # [B, R, R]

        # Compute Frobenius norm of difference
        orth_error = frobenius_norm_complex(product - I)  # [B]

        return torch.mean(orth_error)


class AdaptiveCVCNNLoss(nn.Module):
    """
    Adaptive CV-CNN loss function with learnable orthogonality weights.
    """

    def __init__(self, initial_lambda_rec=1.0, initial_lambda_orth_u=1.0,
                 initial_lambda_orth_v=1.0, adaptation_rate=0.01):
        super().__init__()
        self.base_loss = CVCNNAEAlignedLoss(initial_lambda_rec, initial_lambda_orth_u, initial_lambda_orth_v)
        self.adaptation_rate = adaptation_rate
        self.step_count = 0

        # Learnable weight parameters
        self.log_lambda_orth_u = nn.Parameter(torch.log(torch.tensor(initial_lambda_orth_u)))
        self.log_lambda_orth_v = nn.Parameter(torch.log(torch.tensor(initial_lambda_orth_v)))

    def forward(self, U, S, V, H_gt):
        """Forward pass with adaptive weights."""
        self.step_count += 1

        # Update adaptive weights
        self.base_loss.lambda_orth_u = torch.exp(self.log_lambda_orth_u)
        self.base_loss.lambda_orth_v = torch.exp(self.log_lambda_orth_v)

        return self.base_loss(U, S, V, H_gt)


class RobustCVCNNLoss(nn.Module):
    """
    Robust CV-CNN loss function with Huber-like behavior for outlier resistance.
    """

    def __init__(self, lambda_rec=1.0, lambda_orth_u=1.0, lambda_orth_v=1.0,
                 huber_delta=1.0):
        super().__init__()
        self.lambda_rec = lambda_rec
        self.lambda_orth_u = lambda_orth_u
        self.lambda_orth_v = lambda_orth_v
        self.huber_delta = huber_delta

    def forward(self, U, S, V, H_gt):
        """Forward pass with robust loss."""
        # Get standard loss components
        base_loss = CVCNNAEAlignedLoss(self.lambda_rec, self.lambda_orth_u, self.lambda_orth_v)
        loss_dict = base_loss(U, S, V, H_gt)

        # Apply Huber-like robustness to reconstruction loss
        recon_loss = loss_dict['reconstruction_loss']
        if recon_loss > self.huber_delta:
            robust_recon_loss = self.huber_delta * (2 * torch.sqrt(recon_loss / self.huber_delta) - 1)
        else:
            robust_recon_loss = recon_loss

        # Recompute total loss with robust reconstruction term
        total_loss = (self.lambda_rec * robust_recon_loss +
                     self.lambda_orth_u * loss_dict['orthogonality_loss_u'] +
                     self.lambda_orth_v * loss_dict['orthogonality_loss_v'])

        loss_dict['total_loss'] = total_loss
        loss_dict['robust_reconstruction_loss'] = robust_recon_loss

        return loss_dict


# Legacy alias for backward compatibility
AEAlignedLoss = CVCNNAEAlignedLoss
AdaptiveLoss = AdaptiveCVCNNLoss
RobustLoss = RobustCVCNNLoss


def compute_ae_metric(U, S, V, H_gt):
    """
    Compute the exact AE metric as defined in the competition.
    
    AE = ||H_label - U*S*V^H||_F / ||H_label||_F + ||U^H*U - I||_F + ||V^H*V - I||_F
    
    Args:
        U: Left unitary matrix [B, M, R, 2] or [M, R, 2]
        S: Singular values [B, R] or [R]
        V: Right unitary matrix [B, N, R, 2] or [N, R, 2]
        H_gt: Ground truth channel [B, M, N, 2] or [M, N, 2]
        
    Returns:
        AE metric value
    """
    # Ensure batch dimension
    if U.ndim == 3:
        U = U.unsqueeze(0)
        S = S.unsqueeze(0)
        V = V.unsqueeze(0)
        H_gt = H_gt.unsqueeze(0)
    
    # Convert to complex
    U_c = to_complex(U)
    V_c = to_complex(V)
    S_c = S.type(torch.complex64)
    H_gt_c = to_complex(H_gt)
    
    # 1. Normalized reconstruction error
    U_S = U_c * S_c.unsqueeze(1)
    H_pred = torch.matmul(U_S, torch.conj(V_c).transpose(-2, -1))
    
    recon_error = torch.norm(H_pred - H_gt_c, p='fro', dim=(-2, -1))
    h_norm = torch.norm(H_gt_c, p='fro', dim=(-2, -1))
    normalized_recon_error = recon_error / (h_norm + 1e-8)
    
    # 2. U orthogonality error
    U_H = torch.conj(U_c).transpose(-2, -1)
    UHU = torch.matmul(U_H, U_c)
    I_U = torch.eye(U_c.size(-1), device=U_c.device, dtype=U_c.dtype)
    orth_error_U = torch.norm(UHU - I_U, p='fro', dim=(-2, -1))
    
    # 3. V orthogonality error
    V_H = torch.conj(V_c).transpose(-2, -1)
    VHV = torch.matmul(V_H, V_c)
    I_V = torch.eye(V_c.size(-1), device=V_c.device, dtype=V_c.dtype)
    orth_error_V = torch.norm(VHV - I_V, p='fro', dim=(-2, -1))
    
    # Total AE
    ae = normalized_recon_error + orth_error_U + orth_error_V
    
    return torch.mean(ae)


if __name__ == "__main__":
    # Test the CV-CNN loss functions
    device = "cuda" if torch.cuda.is_available() else "cpu"

    # Create test data
    B, M, N, R = 4, 64, 64, 32
    U = torch.randn(B, M, R, 2, device=device)
    S = torch.rand(B, R, device=device)
    V = torch.randn(B, N, R, 2, device=device)
    H_gt = torch.randn(B, M, N, 2, device=device)

    # Test CV-CNN AE-aligned loss
    loss_fn = CVCNNAEAlignedLoss().to(device)
    loss_dict = loss_fn(U, S, V, H_gt)

    print("CV-CNN Loss components:")
    for key, value in loss_dict.items():
        print(f"  {key}: {value.item():.6f}")

    # Test AE metric computation
    ae_value = compute_ae_metric(U, S, V, H_gt)
    print(f"\nAE metric: {ae_value.item():.6f}")

    # Test adaptive loss
    adaptive_loss_fn = AdaptiveCVCNNLoss().to(device)
    adaptive_loss_dict = adaptive_loss_fn(U, S, V, H_gt)
    print(f"\nAdaptive loss: {adaptive_loss_dict['total_loss'].item():.6f}")
