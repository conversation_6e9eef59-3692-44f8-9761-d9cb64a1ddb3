import os
import glob
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from solution import SVDNet
from loss_functions import CVCNNAEAlignedLoss, AdaptiveCVCNNLoss, compute_ae_metric
import argparse
from tqdm import tqdm
import math
import copy

# ----------------------------------------------------------------------------
# Utility functions
# ----------------------------------------------------------------------------

def read_cfg_file(file_path: str):
    """Parse the cfg txt to obtain antenna dims, IQ channels and rank.
    Returns: (samp_num, M, N, IQ, R)
    """
    with open(file_path, "r") as f:
        lines = [line.strip() for line in f.readlines() if line.strip()]
    samp_num = int(lines[0])
    M = int(lines[1])
    N = int(lines[2])
    IQ = int(lines[3])
    R = int(lines[4])
    return samp_num, M, N, IQ, R


# ----------------------------------------------------------------------------
# Snapshot Ensemble Implementation
# ----------------------------------------------------------------------------

class CyclicCosineAnnealingLR:
    """Cyclic Cosine Annealing Learning Rate Scheduler for Snapshot Ensembles."""

    def __init__(self, optimizer, T_max, eta_min=0, cycles=5):
        self.optimizer = optimizer
        self.T_max = T_max  # Total epochs
        self.eta_min = eta_min
        self.cycles = cycles
        self.cycle_length = T_max // cycles
        self.base_lrs = [group['lr'] for group in optimizer.param_groups]
        self.current_cycle = 0
        self.cycle_epoch = 0

    def step(self, epoch):
        """Update learning rate based on current epoch."""
        self.current_cycle = epoch // self.cycle_length
        self.cycle_epoch = epoch % self.cycle_length

        # Cosine annealing within each cycle
        for param_group, base_lr in zip(self.optimizer.param_groups, self.base_lrs):
            param_group['lr'] = self.eta_min + (base_lr - self.eta_min) * \
                (1 + math.cos(math.pi * self.cycle_epoch / self.cycle_length)) / 2

    def is_snapshot_epoch(self, epoch):
        """Check if current epoch is a snapshot epoch (end of cycle)."""
        return (epoch + 1) % self.cycle_length == 0 and epoch > 0

    def get_last_lr(self):
        """Get current learning rates."""
        return [group['lr'] for group in self.optimizer.param_groups]


class SnapshotEnsemble:
    """Snapshot Ensemble manager for saving and loading model snapshots."""

    def __init__(self, save_dir="snapshots", max_snapshots=5):
        self.save_dir = save_dir
        self.max_snapshots = max_snapshots
        self.snapshots = []

        # Create save directory
        os.makedirs(save_dir, exist_ok=True)

    def save_snapshot(self, model, epoch, loss):
        """Save a model snapshot."""
        snapshot_path = os.path.join(self.save_dir, f"snapshot_epoch_{epoch}_loss_{loss:.6f}.pth")
        torch.save({
            'epoch': epoch,
            'model_state_dict': copy.deepcopy(model.state_dict()),
            'loss': loss
        }, snapshot_path)

        self.snapshots.append({
            'path': snapshot_path,
            'epoch': epoch,
            'loss': loss
        })

        # Keep only the best snapshots
        if len(self.snapshots) > self.max_snapshots:
            # Sort by loss and keep the best ones
            self.snapshots.sort(key=lambda x: x['loss'])
            worst_snapshot = self.snapshots.pop()
            if os.path.exists(worst_snapshot['path']):
                os.remove(worst_snapshot['path'])

        print(f"Saved snapshot at epoch {epoch} with loss {loss:.6f}")

    def load_snapshots(self):
        """Load all saved snapshots."""
        snapshot_files = glob.glob(os.path.join(self.save_dir, "snapshot_*.pth"))
        loaded_snapshots = []

        for snapshot_file in snapshot_files:
            snapshot_data = torch.load(snapshot_file, map_location='cpu')
            loaded_snapshots.append({
                'path': snapshot_file,
                'state_dict': snapshot_data['model_state_dict'],
                'epoch': snapshot_data['epoch'],
                'loss': snapshot_data['loss']
            })

        return loaded_snapshots

# ----------------------------------------------------------------------------
# Dataset definition (memory-mapped to keep RAM usage low)
# ----------------------------------------------------------------------------

class ChannelDataset(Dataset):
    def __init__(self, data_files, label_files):
        assert len(data_files) == len(label_files), "data/label file count mismatch"
        self.data_arrays = [np.load(f, mmap_mode="r") for f in data_files]
        self.label_arrays = [np.load(f, mmap_mode="r") for f in label_files]
        self.cumsum = np.cumsum([arr.shape[0] for arr in self.data_arrays])

    def __len__(self):
        return int(self.cumsum[-1])

    def __getitem__(self, idx):
        file_idx = int(np.searchsorted(self.cumsum, idx, side="right"))
        prev_cum = 0 if file_idx == 0 else self.cumsum[file_idx - 1]
        inner_idx = idx - prev_cum
        H_in = self.data_arrays[file_idx][inner_idx]     # shape [M, N, 2]
        H_gt = self.label_arrays[file_idx][inner_idx]    # ideal channel
        # numpy -> torch
        H_in = torch.from_numpy(H_in).float()
        H_gt = torch.from_numpy(H_gt).float()
        return H_in, H_gt

# ----------------------------------------------------------------------------
# Complex helpers
# ----------------------------------------------------------------------------

def to_complex(t):
    return torch.complex(t[..., 0], t[..., 1])

# ----------------------------------------------------------------------------
# Training loop
# ----------------------------------------------------------------------------

def frob_norm(t):
    """Compute Frobenius norm squared for complex tensor."""
    return torch.sum(torch.abs(t) ** 2, dim=(-2, -1))  # returns [...]


def apply_data_augmentation(H_in, H_gt):
    """Apply data augmentation strategies as described in the solution document.

    Args:
        H_in: Input noisy channel [B, M, N, 2]
        H_gt: Ground truth ideal channel [B, M, N, 2]

    Returns:
        H_in_aug: Augmented input channel
        H_gt: Ground truth channel (unchanged)
    """
    # Convert to complex for augmentation
    H_in_c = to_complex(H_in)

    # 1. Additional noise injection
    noise_std = 0.01  # Small additional noise
    noise = torch.randn_like(H_in_c) * noise_std
    H_in_c = H_in_c + noise

    # 2. Random phase rotation
    batch_size = H_in_c.shape[0]
    theta = torch.rand(batch_size, 1, 1, device=H_in_c.device) * 2 * torch.pi
    phase_rotation = torch.exp(1j * theta)
    H_in_c = H_in_c * phase_rotation

    # Convert back to real-imaginary format
    H_in_aug = torch.stack([H_in_c.real, H_in_c.imag], dim=-1)

    return H_in_aug, H_gt


def apply_enhanced_data_augmentation(H_in, H_gt):
    """Enhanced data augmentation with more sophisticated techniques.

    Args:
        H_in: Input noisy channel [B, M, N, 2]
        H_gt: Ground truth ideal channel [B, M, N, 2]

    Returns:
        H_in_aug: Augmented input channel
        H_gt: Ground truth channel (unchanged)
    """
    # Convert to complex for augmentation
    H_in_c = to_complex(H_in)
    batch_size = H_in_c.shape[0]

    # 1. Adaptive noise injection based on signal strength
    signal_power = torch.mean(torch.abs(H_in_c) ** 2, dim=(-2, -1), keepdim=True)
    noise_std = 0.005 + 0.01 * torch.sqrt(signal_power)  # Adaptive noise level
    noise = torch.randn_like(H_in_c) * noise_std
    H_in_c = H_in_c + noise

    # 2. Random phase rotation (global)
    theta = torch.rand(batch_size, 1, 1, device=H_in_c.device) * 2 * torch.pi
    phase_rotation = torch.exp(1j * theta)
    H_in_c = H_in_c * phase_rotation

    # 3. Random conjugate transpose (with probability 0.5)
    if torch.rand(1).item() > 0.5:
        H_in_c = torch.conj(H_in_c.transpose(-2, -1))

    # 4. Small random scaling
    scale_factor = 1.0 + 0.05 * (torch.rand(batch_size, 1, 1, device=H_in_c.device) - 0.5)
    H_in_c = H_in_c * scale_factor

    # Convert back to real-imaginary format
    H_in_aug = torch.stack([H_in_c.real, H_in_c.imag], dim=-1)

    return H_in_aug, H_gt


def train_with_snapshots(model, dataloader, device="cpu", lr=1e-3, epochs=200, weight_decay=1e-4,
                        cycles=5, lambda_rec=1.0, lambda_orth_u=0.1, lambda_orth_v=0.1,
                        use_adaptive_loss=True):
    """Enhanced training function with snapshot ensembles and adaptive loss.

    Key enhancements:
    - Snapshot ensemble with cyclic cosine annealing
    - Adaptive multi-component loss function aligned with AE metric
    - Enhanced data augmentation
    - Numerical stability improvements
    """
    model.to(device)

    # AdamW optimizer as recommended in the document
    opt = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)

    # Cyclic cosine annealing scheduler for snapshot ensembles
    scheduler = CyclicCosineAnnealingLR(opt, T_max=epochs, cycles=cycles)

    # Snapshot ensemble manager
    snapshot_ensemble = SnapshotEnsemble(save_dir="snapshots", max_snapshots=cycles)

    # CV-CNN loss function with adaptive capability
    if use_adaptive_loss:
        loss_fn = AdaptiveCVCNNLoss(
            initial_lambda_rec=lambda_rec,
            initial_lambda_orth_u=lambda_orth_u,
            initial_lambda_orth_v=lambda_orth_v,
            adaptation_rate=0.01
        ).to(device)
        print("[Training] Using adaptive CV-CNN loss function with learnable orthogonality weights")
    else:
        loss_fn = CVCNNAEAlignedLoss(
            lambda_rec=lambda_rec,
            lambda_orth_u=lambda_orth_u,
            lambda_orth_v=lambda_orth_v
        ).to(device)
        print("[Training] Using fixed-weight CV-CNN AE-aligned loss function")

    for ep in range(1, epochs + 1):
        model.train()
        total_loss = 0.0
        total_recon_loss = 0.0
        total_orth_loss = 0.0

        prog_bar = tqdm(dataloader, desc=f"Epoch {ep}/{epochs} (Cycle {ep//scheduler.cycle_length + 1})")

        for H_in, H_gt in prog_bar:
            H_in = H_in.to(device)
            H_gt = H_gt.to(device)

            # Apply enhanced data augmentation
            H_in_aug, H_gt = apply_enhanced_data_augmentation(H_in, H_gt)

            # Forward pass
            U, S, V = model(H_in_aug)

            # Ensure batch dim
            if U.ndim == 3:  # squeezed when batch==1
                U = U.unsqueeze(0)
                V = V.unsqueeze(0)
                S = S.unsqueeze(0)

            # ----- Enhanced Multi-component loss function (aligned with AE metric) -----
            loss_dict = loss_fn(U, S, V, H_gt)
            total_loss_batch = loss_dict['total_loss']
            recon_loss = loss_dict['reconstruction_loss']
            orth_U = loss_dict['orthogonality_loss_u']
            orth_V = loss_dict['orthogonality_loss_v']

            # Compute AE metric for monitoring
            with torch.no_grad():
                ae_metric = compute_ae_metric(U, S, V, H_gt)

            # Optimization step
            opt.zero_grad()
            total_loss_batch.backward()

            # Gradient clipping for stability
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

            opt.step()

            # Accumulate losses for logging
            total_loss += total_loss_batch.item() * H_in.size(0)
            total_recon_loss += recon_loss.item() * H_in.size(0)
            total_orth_loss += (orth_U.item() + orth_V.item()) * H_in.size(0)

            prog_bar.set_postfix({
                "loss": f"{total_loss_batch.item():.4f}",
                "recon": f"{recon_loss.item():.4f}",
                "orth": f"{(orth_U.item() + orth_V.item()):.2e}",
                "ae": f"{ae_metric.item():.4f}",
                "lr": f"{scheduler.get_last_lr()[0]:.2e}"
            })

        # Update learning rate
        scheduler.step(ep - 1)

        # Compute average losses
        avg_loss = total_loss / len(dataloader.dataset)
        avg_recon_loss = total_recon_loss / len(dataloader.dataset)
        avg_orth_loss = total_orth_loss / len(dataloader.dataset)

        print(f"[Summary] Epoch {ep}/{epochs} | "
              f"Loss={avg_loss:.6f} | "
              f"Recon={avg_recon_loss:.6f} | "
              f"Orth={avg_orth_loss:.2e} | "
              f"LR={scheduler.get_last_lr()[0]:.2e}")

        # Save snapshot at the end of each cycle
        if scheduler.is_snapshot_epoch(ep - 1):
            snapshot_ensemble.save_snapshot(model, ep, avg_loss)
            print(f"Snapshot saved at epoch {ep} (end of cycle {scheduler.current_cycle + 1})")

    return model, snapshot_ensemble

# ----------------------------------------------------------------------------
# Main entry
# ----------------------------------------------------------------------------

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Train Ortho-Efficient SVDNet on provided channel data")
    parser.add_argument("--data_dir", type=str, default="CompetitionData1", help="Directory containing Round1TrainData*.npy")
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu", help="Training device (cuda/cpu)")
    parser.add_argument("--batch_size", type=int, default=128, help="Batch size (64/128 recommended)")
    parser.add_argument("--epochs", type=int, default=200, help="Training epochs (200-300 recommended)")
    parser.add_argument("--lr", type=float, default=1e-5, help="Initial learning rate")
    parser.add_argument("--weight_decay", type=float, default=1e-4, help="Weight decay for regularization")
    # Removed Cayley Transform parameters - now using Exponential Map
    parser.add_argument("--use_adaptive_loss", action="store_true", default=True,
                       help="Use adaptive loss function with learnable weights")
    args = parser.parse_args()

    # Detect training files (Round1)
    data_files = sorted(glob.glob(os.path.join(args.data_dir, "Round1TrainData*.npy")))
    label_files = sorted(glob.glob(os.path.join(args.data_dir, "Round1TrainLabel*.npy")))
    cfg_file = os.path.join(args.data_dir, "Round1CfgData1.txt")  # assume same dims for all
    if not data_files or not label_files:
        raise FileNotFoundError("Training *.npy files not found under given data_dir")

    _, M, N, IQ, R = read_cfg_file(cfg_file)
    print(f"Found {len(data_files)} training files, each dim=({M},{N}), R={R}, device={args.device}")
    print(f"Training CV-CNN SVDNet with Complex-Valued U-Net + Exponential Map Unitary Heads")

    ds = ChannelDataset(data_files, label_files)
    loader = DataLoader(ds, batch_size=args.batch_size, shuffle=True, num_workers=0)

    # Initialize the CV-CNN SVDNet with Exponential Map Unitary Heads
    model = SVDNet(
        dim=M,
        rank=R,
        weight_path=""
    )
    print(f"CV-CNN model initialized with {sum(p.numel() for p in model.parameters())} parameters")
    print(f"Architecture: Complex-Valued U-Net + Exponential Map Unitary Heads")

    # Train the model with enhanced snapshot ensembles and adaptive loss
    trained_model, snapshot_ensemble = train_with_snapshots(
        model, loader,
        device=args.device,
        lr=args.lr,
        epochs=args.epochs,
        weight_decay=args.weight_decay,
        cycles=1,  # Number of snapshot cycles
        lambda_rec=1.0,      # Initial reconstruction loss weight
        lambda_orth_u=0.1,   # Initial U orthogonality loss weight
        lambda_orth_v=0.1,   # Initial V orthogonality loss weight
        use_adaptive_loss=args.use_adaptive_loss  # Configurable adaptive loss weights
    )

    # Save final trained weights
    torch.save(trained_model.state_dict(), "svdnet_final.pth")
    print("Final weights saved to svdnet_final.pth")

    # Print snapshot information
    print(f"\nSnapshot Ensemble Summary:")
    print(f"Total snapshots saved: {len(snapshot_ensemble.snapshots)}")
    for i, snapshot in enumerate(snapshot_ensemble.snapshots):
        print(f"  Snapshot {i+1}: Epoch {snapshot['epoch']}, Loss {snapshot['loss']:.6f}")

    print("CV-CNN training with snapshot ensembles completed successfully!")