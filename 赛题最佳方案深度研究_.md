

# **AI使能的无线鲁棒SVD算子竞赛最优方案深度解析**

## **第一章：竞赛框架的战略性解构**

为了在“AI使能的无线鲁棒SVD算子”竞赛中取得领先，首要任务是超越对赛题规则的表面理解，深入剖析其内在的技术要求与战略要务。本章旨在对竞赛框架进行战略性解构，揭示其核心任务的本质，并基于评价准则制定最优的竞赛策略。

### **1.1 核心任务：一个联合去噪-分解算子**

对赛题任务的初步分析揭示了一个关键事实：该任务并非简单的奇异值分解（SVD）近似。赛题提供的数据结构明确了这一点：模型的输入是非理想信道矩阵 H（例如，RoundYTrainDataX.npy），而模型输出的SVD分量（Uout​, Sout​, Vout​）需要重构的目标是其对应的*理想*信道标签 Hlabel​（例如，RoundYTrainLabelX.npy）。

这一设计将核心任务定义为一个学习驱动的**联合去噪-分解算子**。这意味着所设计的神经网络模型必须隐式地、端到端地完成两个耦合的子任务：

1. **信道去噪与净化 (Denoising/Purification)**：模型必须首先学习一个从非理想信道到理想信道的映射。非理想信道中包含了多种实际通信系统中的损伤，如“复高斯白噪声、定时提前等非理想因素”。因此，网络需要具备强大的鲁棒性，能够从含噪的、失真的输入中恢复出干净的信道结构信息。这本质上是一个高维信号的去噪或图像修复问题。  
2. **矩阵分解 (Decomposition)**：在隐式地完成了信道净化后，模型需要对这个内部生成的、纯净的信道表征进行截断SVD，并输出前 R 个最大的奇异值及其对应的左右奇异向量。

这种双重任务的本质对模型架构设计提出了明确的指导。一个简单的、将输入矩阵直接映射到SVD分量的“黑箱”模型或许能够工作，但其学习效率和最终性能可能受限。一个更具结构化和可解释性的方法是将网络架构在概念上进行拆分，以分别应对这两个子任务。

具体而言，可以将模型设计为“主干网络 (Body)”和“任务头 (Head)”的组合：

* **主干网络 (Body)**：此部分作为核心的特征提取器和去噪滤波器。其设计目标是有效处理信道净化子任务。它可以借鉴计算机视觉领域中先进的图像恢复或去噪架构，如U-Net或基于Transformer的结构。主干网络的职责是从充满噪声和非理想因素的输入 H 中，学习并提取出一个能够代表理想信道 Hlabel​ 的高维特征表示。  
* **任务头 (Head)**：此部分接收来自主干网络的纯净特征表示，并专门负责生成三个SVD输出分量：U、S 和 V。任务头的设计重点是解决分解子任务，特别是要处理好左右奇异矩阵必须满足的酉矩阵（正交）约束这一核心挑战。

这种概念上的分离不仅使设计过程更加清晰，也使得我们可以针对性地为每个子任务选择最先进的技术。主干网络可以利用信号处理和计算机视觉领域的成熟模型，而任务头则需要应用更专业的数学和优化技术，以满足SVD分解的严格数学结构。

### **1.2 评价准则与排名机制的深度剖析**

竞赛的成功不仅取决于模型的理论性能，更直接地由其评价准则和排名规则决定。赛题定义了两个核心评价指标：SVD近似误差（Approximation Error, AE）和模型前向传播的乘加运算次数（Multiply-Accumulate operations, MACs）。

AE的计算公式被定义为：

AEi​=∥Hlabeli​​∥F​∥Hlabeli​​−Ui​Σi​Vi∗​∥F​​+∥Ui∗​Ui​−I∥F​+∥Vi∗​Vi​−I∥F​  
其中，∥⋅∥F​ 表示矩阵的Frobenius范数，I 为单位矩阵。这个公式清晰地揭示了三个评价维度：

1. **重构损失 (Reconstruction Loss)**：第一项衡量的是模型输出的SVD结果重构出的信道与理想信道标签之间的归一化误差。它直接反映了模型SVD近似的准确性。  
2. **左奇异矩阵酉性损失 (Left Unitarity Loss)**：第二项惩罚左奇异矩阵 U 的非酉性。一个理想的酉矩阵 U 必须满足 U∗U=I。  
3. **右奇异矩阵酉性损失 (Right Unitarity Loss)**：第三项同理，惩罚右奇异矩阵 V 的非酉性。

除了AE，MACs作为模型复杂度的度量，也参与最终排名。排名规则为“先按AE整体排名，然后分档位按照复杂度重排”1。这一规则具有深刻的战略指导意义。它明确指出，AE是首要的、决定性的评判标准。MACs仅在AE处于同一“档位”（即性能相近）时才作为次要排序依据。这意味着，一个AE为0.1、复杂度为1 Giga-MACs的方案，其排名将永远高于一个AE为0.11、复杂度仅为1 Mega-MACs的方案。

基于此，一个理性的、最大化获胜概率的竞赛策略应分为两个明确的阶段：

* 第一阶段：追求极致精度 (AE Minimization)  
  在此阶段，核心目标是不计代价地降低AE。应设计和训练最强大、表达能力最强的模型架构，以达到当前技术水平下可能实现的最低AE。这可能意味着使用更深、更宽的网络，或者采用计算量更大的先进模块（如Transformer），暂时将MACs的考量置于次要地位。此阶段的目标是进入AE的第一梯队，为最终排名奠定基础。  
* 第二阶段：效率优化 (Complexity Reduction)  
  在第一阶段获得一个高精度基线模型后，此阶段的重点转向在维持低AE的前提下，系统性地降低MACs。这可以通过一系列模型压缩和优化技术实现，例如：  
  * **知识蒸馏**：使用第一阶段的大模型（教师模型）来指导一个更小、更高效的模型（学生模型）的训练。  
  * **架构剪枝**：系统性地减少网络深度（层数）或宽度（通道数）。  
  * **模块替换**：将模型中计算密集的模块替换为功能相似但计算量更小的模块。

这种两阶段策略确保了资源被优先投入到最关键的性能指标上，完美契合了竞赛的排名逻辑，是通往成功的必经之路。

## **第二章：架构基石：原生复值神经网络**

无线信道本质上是复数域中的物理实体，其信息同时编码在幅度和相位上。为了最真实地模拟底层物理过程并最大化模型性能，采用原生复值神经网络（Complex-Valued Neural Networks, CVNNs）是构建解决方案的架构基石。

### **2.1 复值处理的必要性**

赛题提供的信道数据 H 是复数矩阵，以一个额外的维度2来表示实部和虚部。在无线通信中，尤其是在MIMO系统中，信道矩阵的复数特性至关重要，因为它描述了信号在传播过程中经历的幅度和相位变化。SVD预编码等关键技术正是建立在对复数信道矩阵的分解之上。

一种常见的处理方法是将复数矩阵 H∈CM×N 拆分为两个实数矩阵（实部和虚部），并将它们作为两个独立的通道输入到一个标准的实值神经网络中。然而，这种方法存在根本性的缺陷：它人为地割裂了实部和虚部之间固有的数学关联。复数乘法 (a+ib)(c+id)=(ac−bd)+i(ad+bc) 是一种旋转和缩放的复合操作，而实值网络必须从数据中“重新学习”这一基本规则，这不仅增加了模型的学习负担，降低了效率，还可能因学习不充分而导致性能损失。

相比之下，原生复值神经网络（CVNNs）直接在复数域上进行运算。近年来，CVNNs在处理涉及波、振荡或旋转的物理问题上显示出巨大优势，例如在声学、光学和量子物理领域2。研究表明，CVNNs能够提供更快的收敛速度、更强的泛化能力和更高的预测精度，因为它们的运算（如复数乘法和复数卷积）与底层物理过程的数学描述天然吻合3。

幸运的是，现代深度学习框架（如PyTorch 1.7及以上版本）已经提供了对复数张量（torch.cfloat, torch.cdouble）及其自动微分的成熟支持5。这使得构建端到端的CVNNs变得前所未有地便捷，不再需要依赖过去那些繁琐且低效的变通方案6。

因此，本方案的一个核心架构决策是：**整个模型，从数据输入到最终输出，都应在复数域中进行操作**。所有张量应被视为torch.cfloat类型，所有网络层都应是其复数等价物。这不仅是一种优化，更是对问题物理本质的尊重，是实现SOTA性能的基础。

### **2.2 PyTorch复值层工具箱**

尽管赛题要求使用“标准包，如torch.nn”，但这并不妨碍我们利用这些标准模块构建自定义的复值层。以下是构建一个完整CVNN所需的关键组件及其在PyTorch中的实现策略。

* **复数张量转换**：输入数据 H 的形状为 (Nsamp, M, N, 2)。在送入模型前，应立即使用 torch.view\_as\_complex() 将其转换为形状为 (Nsamp, M, N)、数据类型为 torch.cfloat 的复数张量5。后续所有操作都将基于此复数张量进行。  
* **复数卷积层 (Complex Convolution)**：这是构建基于CNN的去噪主干网络的核心。尽管torch.nn.Conv2d本身尚不支持复数类型的直接计算8，但我们可以通过组合实数卷积来实现一个功能等价且完全合规的复数卷积模块。

  一个复数卷积可以表示为：  
  Conv(XR​+iXI​,WR​+iWI​)=(Conv(XR​,WR​)−Conv(XI​,WI​))+i(Conv(XR​,WI​)+Conv(XI​,WR​))  
  这种朴素实现需要4次实数卷积。一种更高效的实现是利用高斯乘法技巧，它将计算量减少了25%，仅需3次实数卷积9。这对于降低模型的整体MACs至关重要。该自定义模块完全由  
  torch.nn.Conv2d构建，因此符合竞赛规则。类似的逻辑也适用于ComplexConvTranspose2d。  
* **复数全连接层 (Complex Linear)**：torch.nn.Linear 在较新版本的PyTorch中已经原生支持复数张量。当输入和权重张量都为复数类型时，它可以直接使用，无需任何修改5。  
* **复数归一化层 (Complex Normalization)**：  
  * **Layer Normalization**: torch.nn.LayerNorm 是一个很好的选择，因为它原生支持复数张量，并且其计算不依赖于批次统计量，对小批量训练更稳定。  
  * **Batch Normalization**: 简单的做法是分别对实部和虚部应用torch.nn.BatchNorm2d。然而，更严谨的复数批归一化需要考虑实部和虚部之间的协方差，即计算一个 2×2 协方差矩阵并求其逆平方根，如Trabelsi等人的研究中所述6。鉴于其实现复杂度和潜在的数值不稳定性，建议优先使用  
    LayerNorm或朴素的BatchNorm作为起点。

### **2.3 复数激活函数的选择与实现**

激活函数为网络引入了必要的非线性，是模型学习复杂映射能力的关键。对于复数，f(z)，其实值激活函数的推广并非唯一，主要有两种思路11：

1. **分离式激活 (Split Activation)**：将实值激活函数（如ReLU）分别独立地应用于复数的实部和虚部。例如，CReLU(a+ib)=ReLU(a)+i⋅ReLU(b)。这种方法简单直观，但可能破坏复数作为整体的相位信息。  
2. **模-相激活 (Magnitude-Phase Activation)**：将激活函数应用于复数的模（幅度），同时保持其相位不变。例如，modReLU(reiθ)=ReLU(r+b)⋅eiθ，其中 b 是一个可学习的偏置。

对于MIMO信道这类具有明确物理意义的复数数据，**模-相激活是更优越的选择**。相位信息在无线通信中描述了多径信号的相干叠加，是信道特性的核心部分。保留相位信息有助于网络更好地学习和表征信道的物理结构。modReLU便是这种思想的典型代表，它通过对幅度施加非线性来调节信号强度，同时完整地传递相位信息11。

在PyTorch中，modReLU可以作为一个自定义的nn.Module轻松实现，其forward方法利用torch.abs()计算模，torch.angle()计算相位，然后将两者重新组合。其中的偏置项 b 可以作为nn.Parameter进行学习。

## **第三章：巅峰挑战：强制酉性的架构设计**

本次竞赛最核心的技术挑战在于保证输出的左右奇异矩阵 U 和 V 满足酉性（正交性）约束，即 U∗U≈I 和 V∗V≈I。常规神经网络的输出是无结构化的，如何通过架构设计来强制施加这种几何结构，是区分平庸方案与顶尖方案的关键。

### **3.1 隐式正则化 vs. 显式参数化**

解决酉性约束主要有两种途径：

* **隐式正则化 (Implicit Regularization)**：这是赛题背景资料中提到的方法之一。它通过在损失函数中加入酉性惩罚项（如 ∥U∗U−I∥F​）来“引导”一个标准的输出层（如全连接层）生成近似酉矩阵。这是一种“软约束”，优化器需要在巨大的参数空间中自行寻找满足约束的解。  
* **显式参数化 (Explicit Parameterization)**：这种方法从根本上改变了问题。它设计的网络层，其输出在数学构造上**必然**是一个酉矩阵。网络不再学习矩阵的每一个元素，而是学习控制这个酉矩阵生成的更深层次的参数。

**显式参数化在理论上是更优越的解决方案**。隐式正则化将酉性视为一个需要满足的目标，优化过程可能很困难，容易陷入局部最优，最终得到的矩阵也只是“近似”酉。而显式参数化将酉性约束嵌入到网络架构中，将一个在特殊流形（Stiefel流形，即酉矩阵所在的空间）上的**有约束优化问题**，转化为一个在标准欧几里得空间中的**无约束优化问题**12。这对于标准的、基于梯度的优化器（如Adam）来说，是一个更容易解决的问题。网络只需专注于学习如何生成“正确”的酉矩阵，而无需担心其是否“是”一个酉矩阵。这通常会带来更快的收敛速度、更稳定的训练过程，以及一个几乎为零的酉性损失项，从而使优化器能够全力优化核心的重构损失，直接降低总AE。

### **3.2 高级解决方案：可微酉矩阵参数化层**

存在多种技术可以构建输出为酉矩阵的可微层。选择的关键在于表达能力、计算效率和数值稳定性的权衡。

**表1: 酉矩阵参数化技术对比**

| 技术 | 原理 | 优点 | 缺点 | 计算复杂度 | 实现要点 |
| :---- | :---- | :---- | :---- | :---- | :---- |
| **隐式正则化** | 在损失函数中加入酉性惩罚项，引导普通网络层输出近似酉矩阵。 | 实现简单，无需特殊层。 | 软约束，收敛慢，结果非严格酉，可能陷入局部最优。 | O(N2) | 在损失函数中添加 ∥U∗U−I∥ 项。 |
| **指数映射 (Exponential Map)** | 任何酉矩阵 U 可表示为 U=exp(A)，其中 A 为斜埃尔米特矩阵 (A∗=−A) 12。 | 概念清晰，表达能力强（可覆盖整个酉矩阵空间），实现直接。 | 矩阵指数运算 torch.matrix\_exp 计算成本高。 | O(N3) | 从网络输出 K 构造 A=0.5⋅(K−K†)，然后应用 torch.matrix\_exp(A)。 |
| **凯莱变换 (Cayley Transform)** | 酉矩阵可表示为 U=(I+A)(I−A)−1，其中 A 为斜埃尔米特矩阵。 | 避免直接计算矩阵指数。 | 需要矩阵求逆，可能存在数值不稳定性。 | O(N3) | 类似于指数映射，但最后一步是矩阵求逆和乘法。 |
| **吉文斯旋转 (Givens Rotations)** | 将酉矩阵分解为一系列平面旋转（吉文斯旋转）的乘积14。 | 计算效率高，数值稳定，特别适合稀疏或结构化更新13。 | 表达能力可能受限（取决于分解深度），实现相对复杂。 | O(N2) | 网络学习一系列旋转角度，然后构建吉文斯矩阵并连乘。 |

基于上表分析，**指数映射（Exponential Map）** 是作为显式参数化起点的最稳健和直接的选择。虽然其计算复杂度较高，但torch.matrix\_exp函数是PyTorch标准库的一部分，其实现非常直接，并且理论上保证了完备的表达能力。参赛者可以先用此方法确保模型性能的上限，在后续优化阶段再考虑替换为计算效率更高的吉文斯旋转等方法。

指数映射层的具体实现流程如下：

1. 设计一个标准网络模块（如一个小型MLP），其输出为一个复数方阵 K∈Cd×d。  
2. 利用 K 构造一个斜埃尔米特矩阵 A。根据定义 A∗=−A，可以通过 A=21​(K−K∗) 来实现。在PyTorch中，这对应于 A \= 0.5 \* (K \- K.mH)。  
3. 最终的酉矩阵输出即为 U=torch.matrix\_exp(A)。由于torch.matrix\_exp是完全可微的，梯度可以无障碍地反向传播到 K 的参数上。

### **3.3 端到端推荐架构**

结合前述的联合去噪-分解思想、原生复值网络以及酉矩阵参数化技术，我们提出两种具有高度竞争力的端到端架构。

**表2: 推荐模型架构概要**

| 模型 | 组件 | 关键层 | 示例维度 (M=N=64, R=32) | 主要功能 | 任务优势 |
| :---- | :---- | :---- | :---- | :---- | :---- |
| **CV-CNN (U-Net)** | **主干 (去噪)** | ComplexConv2d, modReLU, ComplexLayerNorm, ComplexConvTranspose2d | 输入: (B, 1, 64, 64), 输出: (B, C, 64, 64\) | 多尺度特征提取与信道净化。 | 强大的局部特征捕捉能力，成熟的图像去噪架构，鲁棒性强。 |
|  | **头部 (分解)** | ComplexLinear, **Exponential Map Unitary Layer**, Softplus | 输入: (B, C\*64\*64), 输出: U(B,64,32), S(B,32), V(B,64,32) | 从纯净特征生成SVD分量。 | 显式酉性保证，直接优化AE。 |
| **CV-Transformer (ViT)** | **主干 (去噪)** | ComplexLinear (Patch Embedding), ComplexMultiHeadAttention, ComplexLayerNorm | 输入: (B, 16, 256), 输出: (B, 17, D) | 全局依赖建模与信道净化。 | 擅长捕捉长距离相关性，对XL-MIMO等场景有潜在优势。 |
|  | **头部 (分解)** | ComplexLinear, **Exponential Map Unitary Layer**, Softplus | 输入: (B, D), 输出: U(B,64,32), S(B,32), V(B,64,32) | 从\`\`令牌的输出嵌入生成SVD分量。 | 理论上更强的表达能力，可借鉴NLP/CV领域的最新进展16。 |

#### **3.3.1 稳健基线：带酉性头部的复值U-Net (CV-CNN)**

该架构是首要推荐方案，因为它在性能、鲁棒性和实现可行性之间取得了最佳平衡。

* **主干网络 (Body)**：采用经典的U-Net架构。  
  * **编码器路径**：由一系列我们自定义的ComplexConv2d层、modReLU激活函数和ComplexLayerNorm层组成。通过卷积和下采样（如步长为2的卷积）逐步减少空间维度、增加通道数，从而在不同尺度上提取信道特征。  
  * **解码器路径**：使用ComplexConvTranspose2d（复数转置卷积）进行上采样，将特征图恢复到原始尺寸。  
  * **跳跃连接 (Skip Connections)**：将编码器路径对应尺度的特征图与解码器路径的特征图进行拼接。这是U-Net的关键，它能有效融合高层语义信息和低层细节信息，对于从噪声中精确恢复信道结构至关重要。  
* **任务头 (Head)**：U-Net输出的最终特征图被展平，并送入三个独立的分支：  
  1. **U 分支**：一个小型复值MLP，其最终输出作为参数输入到**指数映射酉性层**，生成 M×R 的酉矩阵 U。  
  2. **V 分支**：结构与U分支类似，生成 N×R 的酉矩阵 V。  
  3. **S 分支**：一个更简单的复值MLP，输出 R 个奇异值。由于奇异值是实数且非负，该分支的输出应取模方或经过一个nn.Softplus层来保证非负性。

#### **3.3.2 高潜力备选：复值Transformer (CV-Transformer)**

该方案风险和潜在回报都更高，可作为第二阶段的探索方向。它直接借鉴了Charton (2021) 将Transformer应用于线性代数计算的思想16。

* **主干网络 (Body)**：基于Vision Transformer (ViT)的思路。  
  * **分块与嵌入**：将输入的 M×N 复数信道矩阵分割成多个小块（patches），例如 16 个 16×16 的块。每个块被展平并通过一个复值线性层映射为一个高维嵌入向量（token）。  
  * **Transformer编码器**：在嵌入序列前加入一个可学习的分类令牌。整个序列（包括令牌和图像块令牌）被送入一连串标准的Transformer编码器模块。所有模块内的多头自注意力（Multi-Head Self-Attention）和前馈网络（Feed-Forward Network）都必须是复值版本。  
* **任务头 (Head)**：训练结束后，取出与\`\`令牌相对应的输出嵌入向量。该向量被认为聚合了整个信道矩阵的全局信息，然后将其送入与CV-CNN方案中相同的、包含三个分支的任务头，以生成U、S和V。

Transformer的自注意力机制使其能够捕捉信道矩阵中任意两个位置之间的长距离依赖关系，这对于未来天线规模更大的XL-MIMO系统可能尤为重要。然而，Transformer通常需要更多的数据和更长的训练时间才能达到最佳性能，且其MACs通常高于CNN。

## **第四章：原则性的训练与优化策略**

拥有先进的架构只是第一步，科学的训练策略是将其潜力完全转化为竞赛成绩的关键。本章将提供一套系统的训练和优化方案。

### **4.1 精心构建多组件损失函数**

损失函数是连接模型输出和优化目标的桥梁。根据评价准则AE的公式，我们的损失函数应直接由其三个部分构成。

在PyTorch中，可以如下实现（假设H\_label, U, S, V均为复数张量）：

Python

\# H\_label: (B, M, N), U: (B, M, R), S: (B, R), V: (B, N, R)  
\# 注意：S是实数向量，需要转换为对角矩阵  
Sigma \= torch.diag\_embed(S.to(torch.cfloat)) \# (B, R, R)

\# 1\. 重构损失  
H\_pred \= U @ Sigma @ V.mH \#.mH是共轭转置  
loss\_recon \= torch.linalg.norm(H\_label \- H\_pred, 'fro') / torch.linalg.norm(H\_label, 'fro')

\# 2\. 左酉性损失  
Id\_M \= torch.eye(U.shape, device=U.device, dtype=torch.cfloat)  
loss\_U \= torch.linalg.norm(U.mH @ U \- Id\_M, 'fro')

\# 3\. 右酉性损失  
Id\_N \= torch.eye(V.shape, device=V.device, dtype=torch.cfloat)  
loss\_V \= torch.linalg.norm(V.mH @ V \- Id\_N, 'fro')

\# 总损失  
total\_loss \= loss\_recon \+ w\_U \* loss\_U \+ w\_V \* loss\_V

其中，w\_U 和 w\_V 是用于平衡三个损失分量的超参数权重。对它们的设置需要根据所选的架构策略进行调整：

* **对于显式酉性参数化架构**：理论上，loss\_U和loss\_V应该接近于零（仅受浮点精度限制）。因此，可以将w\_U和w\_V设为1.0，与AE公式保持一致。甚至可以适当增大它们的值，以更强地惩罚任何数值偏差。  
* **对于隐式正则化架构**：这三个损失项之间存在竞争关系。一种有效的策略是采用课程学习（Curriculum Learning）的思想：在训练初期，设置较小的w\_U和w\_V（例如0.1），让网络优先学习重构任务；随着训练的进行，逐步增大学习率，从而更强地施加酉性约束。这些权重需要通过在DebugData上的实验来精细调整。

### **4.2 分阶段的竞赛策略**

竞赛分为多个阶段，每个阶段提供新的数据集，这要求我们的策略具有适应性和迭代性。

* **调试阶段 (DebugData)**：此阶段的目标是**正确性验证**，而非性能调优。利用DebugData确保整个代码流水线（数据加载、模型前向传播、损失计算、结果保存格式）完全正确无误。  
* **第一轮 (进32强)**：使用CompetitionData1。全力实施第一阶段策略：**最小化AE**。部署推荐的**CV-CNN \+ 指数映射酉性头**架构。此时应将模型容量设置得较大，不必过分担心MACs。目标是确保进入第一性能梯队。  
* **第二、三轮 (进16/8强)**：使用CompetitionData2和CompetitionData3。在拥有高精度模型的基础上，进入第二阶段策略：**效率优化**。  
  1. **探索更高性能**：可以尝试训练**CV-Transformer**架构，看其是否能在新的数据分布上取得更低的AE。  
  2. **降低复杂度**：对已有的高精度CV-CNN模型进行优化，例如减少U-Net的深度/宽度，或者将计算昂贵的指数映射头替换为更高效的吉文斯旋转实现。  
  3. **泛化能力验证**：由于赛题要求对所有三个场景使用同一个模型1，任何架构上的修改都必须在所有已发布的训练集上进行验证，以确保模型的泛化能力没有下降。

### **4.3 超参数与优化器配置**

合理的超参数配置能显著加速模型收敛并提升最终性能。

**表3: 推荐超参数配置起点**

| 超参数 | 推荐值/策略 | 理由/说明 |
| :---- | :---- | :---- |
| **优化器** | AdamW | 对CNN和Transformer都表现出色，集成了权重衰减的改进，鲁棒性强。 |
| **学习率** | 初始值: 1e-4 \~ 3e-4 | 一个相对安全且有效的起点，避免训练初期不稳定。 |
| **学习率调度器** | 带预热的余弦退火 (Cosine Annealing with Warmup) | 训练初期使用较低学习率（预热）稳定模型，之后平滑下降，有助于模型收敛到更优的局部最小值。 |
| **权重衰减 (Weight Decay)** | 1e-2 \~ 1e-1 | AdamW中的权重衰减可以有效正则化模型，防止过拟合。 |
| **批次大小 (Batch Size)** | 尽可能大（根据GPU显存） | 更大的批次大小提供更稳定的梯度估计，通常有利于训练。 |
| **损失函数权重** | w\_U \= 1.0, w\_V \= 1.0 | 作为基线，与AE公式保持一致。可根据4.1节的分析进行调整。 |
| **数据预处理** | 按Frobenius范数归一化 | 将每个输入的信道矩阵 H 除以其自身的Frobenius范数 torch.linalg.norm(H, 'fro')。这能确保输入到网络的数据尺度一致，有助于稳定训练。 |

## **第五章：综合分析与行动建议**

本报告的最终目标是提供一套清晰、可执行的方案，以在竞赛中取得决定性优势。本章将所有分析综合为最终的行动建议。

### **5.1 通往优胜方案的推荐路径**

综合考虑性能、实现难度和创新性，我们为参赛队伍规划了主次两条技术路径。

* 首要推荐方案：复值U-Net (CV-CNN) \+ 指数映射酉性头  
  该方案是通往成功的最稳健、最高效的路径。U-Net是图像到图像任务（如去噪）的黄金标准，其多尺度结构非常适合从受损信道中恢复信息。结合原生复值实现，它能充分利用信道的物理特性。更重要的是，采用指数映射的显式酉性参数化方法，直接解决了竞赛中最棘手的数学约束问题，使优化过程更加直接和高效。该架构代表了当前技术水平下，性能、鲁棒性和实现可行性的最佳结合点。  
* 次要推荐方案：复值Transformer (CV-Transformer)  
  该方案代表了更高风险、更高潜在回报的前沿探索。如果时间和计算资源允许，在CV-CNN方案取得成功后，开发CV-Transformer可能带来性能上的意外突破。其全局注意力机制可能在处理具有复杂非局域相关性的信道（如XL-MIMO）时展现出独特优势。这需要对复值自注意力机制进行严谨的实现，并可能需要更长的训练周期和更多的数据。

### **5.2 平衡精度(AE)与效率(MACs)的实用指南**

遵循两阶段策略，在获得低AE模型后，可按以下步骤系统地优化MACs：

1. **性能剖析**：首先，使用ptflops等工具精确分析模型中每一层的MACs和参数量，定位计算瓶颈。通常，卷积层和全连接层是主要消耗者。  
2. **主干网络简化**：  
   * **减少深度/宽度**：逐步减少U-Net中的卷积层数量或每一层的通道数。  
   * **采用高效卷积**：将部分标准复数卷积替换为深度可分离复数卷积（depthwise separable complex convolution），这能大幅降低计算量。  
3. **任务头优化**：  
   * **替换酉性参数化方法**：将计算成本为 O(N3) 的指数映射层，替换为基于吉文斯旋转的参数化方法。吉文斯旋转的更新成本更低，尤其是当实现为稀疏更新时。  
4. **迭代验证**：每进行一项简化操作，都必须在完整的验证集上重新评估AE。**只接受那些能在显著降低MACs的同时，对AE影响甚微（或无影响）的改动**。这是一个权衡和迭代的过程，目标是在AE-MACs的帕累托前沿上找到最佳点。

### **5.3 实施清单与避坑指南**

为确保顺利实施，请严格遵循以下清单并注意规避常见陷阱。

**实施清单**:

* \[ \] **全程复数**：确保从数据加载开始，所有模型参数和中间张量都使用torch.cfloat类型。  
* \[ \] **实现复数卷积**：构建一个高效且正确的ComplexConv2d模块，推荐使用高斯乘法技巧10。  
* \[ \] **实现复数激活**：实现modReLU作为主要的非线性激活函数11。  
* \[ \] **实现酉性层**：使用torch.matrix\_exp实现指数映射酉性参数化层12。  
* \[ \] **核对损失函数**：确保损失函数使用复数矩阵运算（如.mH共轭转置）精确实现了AE公式的所有三个组成部分。  
* \[ \] **核对输出格式**：严格按照赛题要求，将最终测试结果保存为.npz文件，并使用正确的关键字参数U\_out, S\_out, V\_out。

**常见陷阱规避**:

* **禁止拆分实虚部**：不要将复数矩阵的实部和虚部作为两个独立的通道输入网络。应使用原生的复数张量进行端到端处理。  
* **禁用高相关算子**：在最终提交的solution.py模型定义文件中，严禁出现torch.svd, torch.linalg.svd, torch.qr等被明确禁止的算子1。后台代码检查会扫描这些调用。  
* **注意提交格式**：打包提交.zip文件时，确保将所有文件（.npz, .py, .pth）直接压缩，不要将它们放入一个文件夹后再压缩该文件夹，否则可能导致后台评测失败1。  
* **保持代码结构一致**：不要修改组委会提供的solution.py文件名，以及其中的SVDNet类名和函数结构1。

通过遵循本报告中提出的深度分析、战略规划和技术蓝图，参赛队伍将能够系统性地应对本次竞赛的各项挑战，从而在激烈的竞争中占据有利地位。

#### **引用的著作**

1. QA.docx  
2. Complex Valued Neural Networks for Physics Applications \- Wavefrontshaping.net, 访问时间为 八月 4, 2025， [https://www.wavefrontshaping.net/post/id/1](https://www.wavefrontshaping.net/post/id/1)  
3. Complex-valued physics-informed machine learning for efficient solving of quintic nonlinear Schr\\"odinger equations | Phys. Rev. Research, 访问时间为 八月 4, 2025， [https://link.aps.org/doi/10.1103/PhysRevResearch.7.013164](https://link.aps.org/doi/10.1103/PhysRevResearch.7.013164)  
4. Complex-Valued Physics-Informed Neural Network for Near-Field Acoustic Holography \- European Association For Signal Processing, 访问时间为 八月 4, 2025， [https://eurasip.org/Proceedings/Eusipco/Eusipco2024/pdfs/0000126.pdf](https://eurasip.org/Proceedings/Eusipco/Eusipco2024/pdfs/0000126.pdf)  
5. Complex Numbers — PyTorch 2.7 documentation, 访问时间为 八月 4, 2025， [https://pytorch.org/docs/stable/complex\_numbers.html](https://pytorch.org/docs/stable/complex_numbers.html)  
6. wavefrontshaping/complexPyTorch: A high-level toolbox for using complex valued neural networks in PyTorch \- GitHub, 访问时间为 八月 4, 2025， [https://github.com/wavefrontshaping/complexPyTorch](https://github.com/wavefrontshaping/complexPyTorch)  
7. XinyuanLiao/ComplexNN: A toolbox for using complex valued standard network modules in PyTorch. \- GitHub, 访问时间为 八月 4, 2025， [https://github.com/XinyuanLiao/ComplexNN](https://github.com/XinyuanLiao/ComplexNN)  
8. Complex-valued CNN layers \- PyTorch Forums, 访问时间为 八月 4, 2025， [https://discuss.pytorch.org/t/complex-valued-cnn-layers/148491](https://discuss.pytorch.org/t/complex-valued-cnn-layers/148491)  
9. Native implementation of convolution for complex numbers · Issue \#52983 \- GitHub, 访问时间为 八月 4, 2025， [https://github.com/pytorch/pytorch/issues/52983](https://github.com/pytorch/pytorch/issues/52983)  
10. Convolution Layers — complextorch 1.0.0 documentation, 访问时间为 八月 4, 2025， [https://complextorch.readthedocs.io/en/stable/nn/modules/conv.html](https://complextorch.readthedocs.io/en/stable/nn/modules/conv.html)  
11. Activation functions for complex tensors · Issue \#47052 \- GitHub, 访问时间为 八月 4, 2025， [https://github.com/pytorch/pytorch/issues/47052](https://github.com/pytorch/pytorch/issues/47052)  
12. Cheap Orthogonal Constraints in Neural Networks: A Simple Parametrization of the Orthogonal and Unitary Group \- Proceedings of Machine Learning Research, 访问时间为 八月 4, 2025， [http://proceedings.mlr.press/v97/lezcano-casado19a/lezcano-casado19a.pdf](http://proceedings.mlr.press/v97/lezcano-casado19a/lezcano-casado19a.pdf)  
13. Coordinate descent on the Stiefel manifold for deep neural network training, 访问时间为 八月 4, 2025， [https://www.esann.org/sites/default/files/proceedings/2023/ES2023-143.pdf](https://www.esann.org/sites/default/files/proceedings/2023/ES2023-143.pdf)  
14. Complex Unitary Recurrent Neural Networks Using Scaled Cayley Transform, 访问时间为 八月 4, 2025， [https://ojs.aaai.org/index.php/AAAI/article/view/4371/4249](https://ojs.aaai.org/index.php/AAAI/article/view/4371/4249)  
15. Givens rotation \- Wikipedia, 访问时间为 八月 4, 2025， [https://en.wikipedia.org/wiki/Givens\_rotation](https://en.wikipedia.org/wiki/Givens_rotation)  
16. Int2Int: a framework for mathematics with transformers \- arXiv, 访问时间为 八月 4, 2025， [https://arxiv.org/pdf/2502.17513](https://arxiv.org/pdf/2502.17513)  
17. \[2112.01898\] Linear algebra with transformers \- arXiv, 访问时间为 八月 4, 2025， [https://arxiv.org/abs/2112.01898](https://arxiv.org/abs/2112.01898)  
18. Linear algebra with transformers, 访问时间为 八月 4, 2025， [https://arxiv.org/pdf/2112.01898](https://arxiv.org/pdf/2112.01898)